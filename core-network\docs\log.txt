2025-08-04 19:49:48.076 CNET-SERVICE-UnifiedAi   D  📡 [流式AI请求] 发送到: https://catsapi.com/v1/chat/completions
2025-08-04 19:49:48.104 TB-VM                    D  🚀 [ViewModel初始化] ThinkingBoxViewModel启动
2025-08-04 19:49:48.109 CNET-SERVICE-UnifiedAi   I  📥 [HTTP响应流开始] 开始接收SSE数据流: messageId=msg_CPPHVN
2025-08-04 19:49:48.119 CNET-REST-Client         I  🌊 [真实流式请求开始] POST https://catsapi.com/v1/chat/completions, 总请求数=1
2025-08-04 19:49:48.316 TB-COACH                 I  🚀 ThinkingBox激活: messageId=msg_CPPHVN, state=AwaitingFirstToken
2025-08-04 19:49:48.318 TB-Thinkin...thCallback  I  TB-API: 🔗 【Plan B重构】设置ThinkingBox完成回调: messageId=msg_CPPHVN
2025-08-04 19:49:48.319 TB-VM                    D  🔗 [回调设置] CompletionListener已设置: true
2025-08-04 19:49:48.321 TB-REDUCER-Segment       D  🔄 [Intent处理] Initialize
2025-08-04 19:49:48.322 TB-VM-Main               D  🔍 [数据流] ViewModel激活: messageId=msg_CPPHVN
2025-08-04 19:49:48.322 TB-INIT                  I  🚀 [初始化] ThinkingBox: messageId=msg_CPPHVN
2025-08-04 19:49:48.383 TB-EFFECT                D  ⚡ [Effect处理] StartTokenStreamListening
2025-08-04 19:49:48.383 TB-STREAM                I  🎯 [Token流启动] messageId=msg_CPPHVN
2025-08-04 19:49:48.386 TB-STREAM                I  ✅ [直接订阅] DirectOutputChannel: messageId=msg_CPPHVN
2025-08-04 19:49:48.386 CNET-OUTPUT-Direct       D  🔗 [订阅] messageId=msg_CPPHVN, 活跃订阅者=1
2025-08-04 19:49:48.396 TB-PARSER-Stream         I  🚀 [解析启动] 开始解析token流: messageId=msg_CPPHVN
2025-08-04 19:49:48.404 TB-Thinkin...thCallback  D  TB-API: ✅ 【Plan B重构】ThinkingBox初始化完成
2025-08-04 19:49:48.405 TB-Thinkin...oxInternal  D  TB-VM:  🚀 初始化ThinkingBox: msg_CPPHVN, hasTokenFlow=false
2025-08-04 19:49:48.406 TB-Thinkin...elProvider  D  TB-Provider:TB-Provider: 🔗 [ThinkingBoxViewModelProvider] 注册ViewModel: messageId=msg_CPPHVN
2025-08-04 19:49:48.408 TB-REDUCER-Segment       D  🔄 [Intent处理] Initialize
2025-08-04 19:49:48.408 TB-VM-Main               D  🔍 [数据流] ViewModel激活: messageId=msg_CPPHVN
2025-08-04 19:49:48.408 TB-INIT                  I  🚀 [初始化] ThinkingBox: messageId=msg_CPPHVN
2025-08-04 19:49:48.408 TB-INIT                  D  ⏭️ [跳过重复] 初始化: msg_CPPHVN
2025-08-04 19:49:48.409 TB-Thinkin...oxInternal  D  TB-VM:  🔗 连接HistoryActor到Effect流: msg_CPPHVN
2025-08-04 19:49:48.416 TB-HistoryActor          I  TB-History: 🚀 [初始化] 开始监听ThinkingBox Effect流
2025-08-04 19:49:48.430 Coach-ThinkingBox        D  🔍 [显示决策] isStreaming=true
2025-08-04 19:49:48.435 TB-COACH                 I  🔍 AwaitingFirstToken状态: 实际AI消息ID=msg_CPPHVN
2025-08-04 19:49:48.473 Coach-ThinkingBox        D  🔍 [显示决策] isStreaming=true
