
2025-08-04 19:49:48.408 TB-REDUCER-Segment       D  🔄 [Intent处理] Initialize
2025-08-04 19:49:48.408 TB-VM-Main               D  🔍 [数据流] ViewModel激活: messageId=msg_CPPHVN
2025-08-04 19:49:48.408 TB-INIT                  I  🚀 [初始化] ThinkingBox: messageId=msg_CPPHVN
2025-08-04 19:49:48.408 TB-INIT                  D  ⏭️ [跳过重复] 初始化: msg_CPPHVN
2025-08-04 19:49:48.409 TB-Thinkin...oxInternal  D  TB-VM:  🔗 连接HistoryActor到Effect流: msg_CPPHVN
2025-08-04 19:49:48.416 TB-HistoryActor          I  TB-History: 🚀 [初始化] 开始监听ThinkingBox Effect流
2025-08-04 19:49:48.430 Coach-ThinkingBox        D  🔍 [显示决策] isStreaming=true
2025-08-04 19:49:48.435 TB-COACH                 I  🔍 AwaitingFirstToken状态: 实际AI消息ID=msg_CPPHVN
2025-08-04 19:49:48.473 Coach-ThinkingBox        D  🔍 [显示决策] isStreaming=true
2025-08-04 19:53:21.037 RetryInterceptor         W  🔄 网络异常，第1次重试: https://catsapi.com/v1/chat/completions
2025-08-04 19:53:21.037 RetryInterceptor         W  java.net.SocketTimeoutException: failed to connect to catsapi.com/2606:4700:3030::6815:6001 (port 443) from /fec0::5054:ff:fe12:3456 (port 45090) after 30000ms
2025-08-04 19:53:21.037 RetryInterceptor         W  	at libcore.io.IoBridge.connectErrno(IoBridge.java:235)
2025-08-04 19:53:21.037 RetryInterceptor         W  	at libcore.io.IoBridge.connect(IoBridge.java:179)
2025-08-04 19:53:21.037 RetryInterceptor         W  	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
2025-08-04 19:53:21.037 RetryInterceptor         W  	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
2025-08-04 19:53:21.038 RetryInterceptor         W  	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
2025-08-04 19:53:21.038 RetryInterceptor         W  	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
2025-08-04 19:53:21.038 RetryInterceptor         W  	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
2025-08-04 19:53:21.038 RetryInterceptor         W  	at java.net.Socket.connect(Socket.java:646)
2025-08-04 19:53:21.038 RetryInterceptor         W  	at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
2025-08-04 19:53:21.039 RetryInterceptor         W  	at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
2025-08-04 19:53:21.039 RetryInterceptor         W  	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207)
2025-08-04 19:53:21.039 RetryInterceptor         W  	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
2025-08-04 19:53:21.040 RetryInterceptor         W  	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
2025-08-04 19:53:21.040 RetryInterceptor         W  	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
2025-08-04 19:53:21.040 RetryInterceptor         W  	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
2025-08-04 19:53:21.040 RetryInterceptor         W  	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
2025-08-04 19:53:21.040 RetryInterceptor         W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-08-04 19:53:21.040 RetryInterceptor         W  	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
2025-08-04 19:53:21.040 RetryInterceptor         W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-08-04 19:53:21.040 RetryInterceptor         W  	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
2025-08-04 19:53:21.040 RetryInterceptor         W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-08-04 19:53:21.040 RetryInterceptor         W  	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)

2025-08-04 19:53:21.044 RetryInterceptor         W  	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)
2025-08-04 19:53:21.044 RetryInterceptor         W  	Suppressed: java.net.SocketException: Connection reset
2025-08-04 19:53:21.044 RetryInterceptor         W  		at java.net.SocketInputStream.read(SocketInputStream.java:191)
2025-08-04 19:53:21.044 RetryInterceptor         W  		at java.net.SocketInputStream.read(SocketInputStream.java:143)
2025-08-04 19:53:21.045 RetryInterceptor         W  		at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:994)
2025-08-04 19:53:21.045 RetryInterceptor         W  		at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:958)
2025-08-04 19:53:21.045 RetryInterceptor         W  		at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
2025-08-04 19:53:21.045 RetryInterceptor         W  		at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:242)
2025-08-04 19:53:21.045 RetryInterceptor         W  		at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:224)
2025-08-04 19:53:21.046 RetryInterceptor         W  		at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
2025-08-04 19:53:21.046 RetryInterceptor         W  		at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
2025-08-04 19:53:21.046 RetryInterceptor         W  		at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
2025-08-04 19:53:21.046 RetryInterceptor         W  		... 55 more
2025-08-04 19:53:21.052 RetryInterceptor         W  	Suppressed: java.net.SocketException: Connection reset
2025-08-04 19:53:21.053 RetryInterceptor         W  		at java.net.SocketInputStream.read(SocketInputStream.java:191)
2025-08-04 19:53:21.053 RetryInterceptor         W  		at java.net.SocketInputStream.read(SocketInputStream.java:143)
2025-08-04 19:53:21.053 RetryInterceptor         W  		at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:994)
2025-08-04 19:53:21.053 RetryInterceptor         W  		at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:958)
2025-08-04 19:53:21.053 RetryInterceptor         W  		at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
2025-08-04 19:53:21.053 RetryInterceptor         W  		at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:242)
2025-08-04 19:53:21.053 RetryInterceptor         W  		at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:224)
2025-08-04 19:53:21.054 RetryInterceptor         W  		at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
2025-08-04 19:53:21.054 RetryInterceptor         W  		at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
2025-08-04 19:53:21.054 RetryInterceptor         W  		at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
2025-08-04 19:53:21.054 RetryInterceptor         W  		... 55 more
2025-08-04 19:53:21.054 RetryInterceptor         W  	Suppressed: java.net.SocketTimeoutException: failed to connect to catsapi.com/2606:4700:3030::6815:5001 (port 443) from /fec0::5054:ff:fe12:3456 (port 38190) after 30000ms
2025-08-04 19:53:21.054 RetryInterceptor         W  		... 66 more
2025-08-04 19:53:21.055 RetryInterceptor         W  	Suppressed: java.net.SocketTimeoutException: failed to connect to catsapi.com/2606:4700:3030::6815:4001 (port 443) from /fec0::5054:ff:fe12:3456 (port 34706) after 30000ms
2025-08-04 19:53:21.055 RetryInterceptor         W  		... 66 more
2025-08-04 19:53:21.055 RetryInterceptor         W  	Suppressed: java.net.SocketTimeoutException: failed to connect to catsapi.com/2606:4700:3030::6815:7001 (port 443) from /fec0::5054:ff:fe12:3456 (port 53372) after 30000ms
2025-08-04 19:53:21.055 RetryInterceptor         W  		... 66 more
2025-08-04 19:53:21.056 RetryInterceptor         W  	Suppressed: java.net.SocketTimeoutException: failed to connect to catsapi.com/2606:4700:3030::6815:3001 (port 443) from /fec0::5054:ff:fe12:3456 (port 43834) after 30000ms
2025-08-04 19:53:21.056 RetryInterceptor         W  		... 66 more
2025-08-04 19:53:21.056 RetryInterceptor         W  	Suppressed: java.net.SocketTimeoutException: failed to connect to catsapi.com/2606:4700:3030::6815:1001 (port 443) from /fec0::5054:ff:fe12:3456 (port 54368) after 30000ms
2025-08-04 19:53:21.056 RetryInterceptor         W  		... 66 more
2025-08-04 19:53:21.056 RetryInterceptor         W  	Suppressed: java.net.SocketTimeoutException: failed to connect to catsapi.com/2606:4700:3030::6815:2001 (port 443) from /fec0::5054:ff:fe12:3456 (port 35368) after 29999ms
2025-08-04 19:53:21.056 RetryInterceptor         W  		... 66 more
2025-08-04 19:53:23.061 RetryInterceptor         W  🔄 [重试统计] 第1次重试, 原因: SocketTimeoutException - failed to connect to catsapi.com/2606:4700:3030::6815:6001 (port 443) from /fec0::5054:ff:fe12:3456 (port 45090) after 30000ms
2025-08-04 19:53:29.549 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#1] data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-qwen3-8b:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":"嗯","reasoning_details":[]},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}
2025-08-04 19:53:29.550 CNET-SERVICE-UnifiedAi   D  📡 [SSE数据#1] 内容长度=338, 预览='{"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-q...'
2025-08-04 19:53:29.551 CNET-SERVICE-UnifiedAi   V  📨 [原始Token接收] messageId=msg_CPPHVN, 长度=344, 预览='data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-...'
2025-08-04 19:53:29.551 CNET-PROCESSOR-Stream    I  📊 [批量统计] 已处理tokens=1, 解析错误=0, 错误率=0%
2025-08-04 19:53:29.605 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=344->0
2025-08-04 19:53:29.606 CNET-SERVICE-UnifiedAi   V  ⏭️ [Token跳过] messageId=msg_CPPHVN, 处理后为空，跳过发送
2025-08-04 19:53:29.606 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#2]
2025-08-04 19:53:29.606 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
2025-08-04 19:53:29.608 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#3] data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-qwen3-8b:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":"，","reasoning_details":[]},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}
2025-08-04 19:53:29.608 CNET-SERVICE-UnifiedAi   D  📡 [SSE数据#2] 内容长度=338, 预览='{"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-q...'
2025-08-04 19:53:29.608 CNET-SERVICE-UnifiedAi   V  📨 [原始Token接收] messageId=msg_CPPHVN, 长度=344, 预览='data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-...'
2025-08-04 19:53:29.609 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=344->0
2025-08-04 19:53:29.610 CNET-SERVICE-UnifiedAi   V  ⏭️ [Token跳过] messageId=msg_CPPHVN, 处理后为空，跳过发送
2025-08-04 19:53:29.612 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#4]
2025-08-04 19:53:29.612 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
2025-08-04 19:53:29.613 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#5] data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-qwen3-8b:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":"用户","reasoning_details":[]},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}
2025-08-04 19:53:29.614 CNET-SERVICE-UnifiedAi   D  📡 [SSE数据#3] 内容长度=339, 预览='{"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-q...'
2025-08-04 19:53:29.615 CNET-SERVICE-UnifiedAi   V  📨 [原始Token接收] messageId=msg_CPPHVN, 长度=345, 预览='data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-...'
2025-08-04 19:53:29.616 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=345->0
2025-08-04 19:53:29.620 CNET-SERVICE-UnifiedAi   V  ⏭️ [Token跳过] messageId=msg_CPPHVN, 处理后为空，跳过发送
2025-08-04 19:53:29.622 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#6]
2025-08-04 19:53:29.622 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
2025-08-04 19:53:29.623 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#7] data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-qwen3-8b:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":"想","reasoning_details":[]},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}
2025-08-04 19:53:29.625 CNET-SERVICE-UnifiedAi   D  📡 [SSE数据#4] 内容长度=338, 预览='{"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-q...'
2025-08-04 19:53:29.629 CNET-SERVICE-UnifiedAi   V  📨 [原始Token接收] messageId=msg_CPPHVN, 长度=344, 预览='data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-...'
2025-08-04 19:53:29.630 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=344->0
2025-08-04 19:53:29.631 CNET-SERVICE-UnifiedAi   V  ⏭️ [Token跳过] messageId=msg_CPPHVN, 处理后为空，跳过发送
2025-08-04 19:53:29.633 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#8]
2025-08-04 19:53:29.633 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
2025-08-04 19:53:29.634 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#9] data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-qwen3-8b:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":"开始","reasoning_details":[]},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}
2025-08-04 19:53:29.634 CNET-SERVICE-UnifiedAi   D  📡 [SSE数据#5] 内容长度=339, 预览='{"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-q...'
2025-08-04 19:53:29.634 CNET-SERVICE-UnifiedAi   V  📨 [原始Token接收] messageId=msg_CPPHVN, 长度=345, 预览='data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-...'
2025-08-04 19:53:29.635 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=345->0
2025-08-04 19:53:29.635 CNET-SERVICE-UnifiedAi   V  ⏭️ [Token跳过] messageId=msg_CPPHVN, 处理后为空，跳过发送
2025-08-04 19:53:29.635 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#10]
2025-08-04 19:53:29.635 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
2025-08-04 19:53:29.635 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#11] data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-qwen3-8b:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":"力量","reasoning_details":[]},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}
2025-08-04 19:53:29.636 CNET-SERVICE-UnifiedAi   D  📡 [SSE数据#6] 内容长度=339, 预览='{"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-q...'
2025-08-04 19:53:29.636 CNET-SERVICE-UnifiedAi   V  📨 [原始Token接收] messageId=msg_CPPHVN, 长度=345, 预览='data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-...'
2025-08-04 19:53:29.638 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=345->0
2025-08-04 19:53:29.638 CNET-SERVICE-UnifiedAi   V  ⏭️ [Token跳过] messageId=msg_CPPHVN, 处理后为空，跳过发送
2025-08-04 19:53:29.638 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#12]
2025-08-04 19:53:29.639 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
2025-08-04 19:53:29.826 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#13] data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-qwen3-8b:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":"训练","reasoning_details":[]},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}
2025-08-04 19:53:29.826 CNET-SERVICE-UnifiedAi   D  📡 [SSE数据#7] 内容长度=339, 预览='{"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-q...'
2025-08-04 19:53:29.826 CNET-SERVICE-UnifiedAi   V  📨 [原始Token接收] messageId=msg_CPPHVN, 长度=345, 预览='data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-...'
2025-08-04 19:53:29.827 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=345->0
2025-08-04 19:53:29.828 CNET-SERVICE-UnifiedAi   V  ⏭️ [Token跳过] messageId=msg_CPPHVN, 处理后为空，跳过发送
2025-08-04 19:53:29.828 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#14]
2025-08-04 19:53:29.828 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
2025-08-04 19:53:29.843 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#15] data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-qwen3-8b:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":"，","reasoning_details":[]},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}
2025-08-04 19:53:29.843 CNET-SERVICE-UnifiedAi   D  📡 [SSE数据#8] 内容长度=338, 预览='{"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-q...'
2025-08-04 19:53:29.844 CNET-SERVICE-UnifiedAi   V  📨 [原始Token接收] messageId=msg_CPPHVN, 长度=344, 预览='data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-...'
2025-08-04 19:53:29.845 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=344->0
2025-08-04 19:53:29.848 CNET-SERVICE-UnifiedAi   V  ⏭️ [Token跳过] messageId=msg_CPPHVN, 处理后为空，跳过发送
2025-08-04 19:53:29.849 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#16]
2025-08-04 19:53:29.849 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
2025-08-04 19:53:29.849 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#17] data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-qwen3-8b:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":"但","reasoning_details":[]},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}
2025-08-04 19:53:29.849 CNET-SERVICE-UnifiedAi   D  📡 [SSE数据#9] 内容长度=338, 预览='{"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-q...'
2025-08-04 19:53:29.850 CNET-SERVICE-UnifiedAi   V  📨 [原始Token接收] messageId=msg_CPPHVN, 长度=344, 预览='data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-...'
2025-08-04 19:53:29.850 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=344->0
2025-08-04 19:53:29.851 CNET-SERVICE-UnifiedAi   V  ⏭️ [Token跳过] messageId=msg_CPPHVN, 处理后为空，跳过发送
2025-08-04 19:53:29.851 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#18]
2025-08-04 19:53:29.851 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
