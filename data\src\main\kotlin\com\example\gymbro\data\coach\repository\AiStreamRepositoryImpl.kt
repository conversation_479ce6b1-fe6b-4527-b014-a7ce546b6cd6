package com.example.gymbro.data.coach.repository

import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.core.conversation.ConversationIdManager
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.GymBroLogTags
import com.example.gymbro.data.coach.service.AiRequestSender
// 🧹 REMOVED: import com.example.gymbro.data.coach.service.AiResponseReceiver (架构简化)
import com.example.gymbro.domain.coach.model.AiTaskType
import com.example.gymbro.domain.coach.model.StreamEvent
import com.example.gymbro.domain.coach.repository.AiStreamRepository
import com.example.gymbro.domain.coach.repository.TaskCapabilities
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【架构重构】AI流式仓库实现 - 直接调用Core-Network
 *
 * 新职责（简化后）：
 * - 请求构建（通过AiRequestSender）
 * - 直接委托给Core-Network统一处理
 * - 消除中间层，建立唯一数据链路
 *
 * 🚫 移除的职责：
 * - ❌ 已移除AiResponseReceiver中间层 (架构简化)
 * - ❌ 消除重复的请求发送逻辑
 */
@Singleton
class AiStreamRepositoryImpl @Inject constructor(
    private val aiRequestSender: AiRequestSender,
    private val unifiedAiResponseService: com.example.gymbro.core.network.service.UnifiedAiResponseService,
) : AiStreamRepository {

    override suspend fun insertThinking(
        sessionId: String,
        prompt: String,
    ): ModernResult<String> = aiRequestSender.insertThinking(sessionId, prompt)

    /**
     * 🔥 【阶段3重构】接收已构建的消息列表，使用 MessageContext 统一ID管理
     */
    override suspend fun streamAiResponse(
        messageContext: ConversationIdManager.MessageContext,
        messages: List<CoreChatMessage>,
        taskType: AiTaskType,
    ): Flow<StreamEvent> = kotlinx.coroutines.flow.flow {
        Timber.d("🔄 [阶段3重构] 开始流式AI响应 - ${messageContext.getDebugInfo()}")

        try {
            // 发送首包 Thinking 事件
            emit(
                StreamEvent.Thinking(
                    sessionId = messageContext.sessionId,
                    messageId = messageContext.messageId,
                    timestamp = System.currentTimeMillis(),
                )
            )
            Timber.d("✅ 发送首包Thinking事件: ${messageContext.getDisplayId()}")

            // 构建 ChatRequest
            val chatRequest = com.example.gymbro.shared.models.ai.ChatRequest(
                model = "deepseek-chat",
                messages = messages.map { coreMsg ->
                    com.example.gymbro.shared.models.ai.ChatMessage(
                        role = coreMsg.role,
                        content = coreMsg.content,
                    )
                },
                stream = true,
                maxTokens = 4000,
                temperature = 0.7,
            )

            // 委托给 streamChatWithMessageId 处理实际的AI请求
            var contentBuilder = StringBuilder()
            streamChatWithMessageId(chatRequest, messageContext.messageId, taskType)
                .collect { outputToken ->
                    contentBuilder.append(outputToken.content)

                    // 转换为 StreamEvent.Chunk
                    emit(
                        StreamEvent.Chunk(
                            sessionId = messageContext.sessionId,
                            messageId = messageContext.messageId,
                            content = outputToken.content,
                            timestamp = outputToken.timestamp,
                        )
                    )
                }

            // 发送完成事件
            emit(
                StreamEvent.Done(
                    sessionId = messageContext.sessionId,
                    messageId = messageContext.messageId,
                    fullText = contentBuilder.toString(),
                    timestamp = System.currentTimeMillis(),
                )
            )
            Timber.d("✅ 流式AI响应完成: ${messageContext.getDisplayId()}")

        } catch (e: Exception) {
            Timber.e(e, "❌ 流式AI响应失败: ${messageContext.getDisplayId()}")

            // 发送错误事件
            emit(
                StreamEvent.Error(
                    sessionId = messageContext.sessionId,
                    messageId = messageContext.messageId,
                    error = e,
                    timestamp = System.currentTimeMillis(),
                )
            )
        }
    }

    /**
     * 🔥 【架构重构】直接调用Core-Network，消除中间层
     */
    override suspend fun streamChatWithMessageId(
        request: ChatRequest,
        messageId: String,
        taskType: AiTaskType,
    ): Flow<com.example.gymbro.core.network.output.OutputToken> {
        Timber.d("🔄 [简化架构] 直接调用Core-Network - messageId=$messageId, taskType=$taskType")

        // 优化请求参数
        val optimizedRequest = aiRequestSender.optimizeRequestForTask(request, taskType)

        // 🔥 【数据流验证】记录 AiStreamRepository 调用
        Timber.tag(GymBroLogTags.Data.REPOSITORY).d("🔍 [数据流] AiStreamRepository调用: messageId=$messageId")

        // 🔥 【架构重构】启动Core-Network处理，但不返回实际token流给Coach
        // Token会自动通过DirectOutputChannel路由到ThinkingBox，Coach通过回调接收最终结果
        return flow {
            try {
                // 启动UnifiedAiResponseService处理，触发DirectOutputChannel数据流
                unifiedAiResponseService.processAiStreamingResponse(optimizedRequest, messageId)
                    .collect { processedToken ->
                        // 🔥 【职责分离】不emit给Coach，让token通过DirectOutputChannel → ThinkingBox
                        // Coach将通过ThinkingBoxCompletionListener回调接收最终结果
                        Timber.tag("AI-STREAM-REPO").v("🔄 Token已路由到DirectOutputChannel: messageId=$messageId, 长度=${processedToken.length}")
                    }

                // 🔥 【向后兼容】为了不破坏现有的collect调用，发送一个空的完成标记
                emit(
                    com.example.gymbro.core.network.output.OutputToken(
                        content = "", // 空内容，表示流启动完成
                        messageId = messageId,
                        contentType = com.example.gymbro.core.network.detector.ContentType.JSON_SSE,
                        timestamp = System.currentTimeMillis(),
                        metadata = mapOf("source" to "stream-initiated", "note" to "tokens-routed-to-thinkingbox"),
                    )
                )
            } catch (e: Exception) {
                Timber.tag("AI-STREAM-REPO").e(e, "❌ AI流启动失败: messageId=$messageId")
                throw e
            }
        }
    }

    /**
     * 获取任务类型支持的功能
     */
    override suspend fun getTaskCapabilities(taskType: AiTaskType): TaskCapabilities {
        return aiRequestSender.getTaskCapabilities(taskType)
    }

    /**
     * 获取监控指标
     * 🧹 REMOVED: aiResponseReceiver调用 (架构简化)
     */
    fun getMetrics(): String {
        return "Metrics temporarily unavailable - migrated to event bus architecture"
    }
}
