package com.example.gymbro.features.thinkingbox.internal.contract

import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * ThinkingBoxContract测试 - Plan B重构版本
 *
 * 🔥 【Plan B重构】验证Contract结构正确性，确保sessionId移除后的功能完整性
 */
class ThinkingBoxContractTest {

    @Test
    fun `should initialize state correctly`() {
        // Given & When
        val state = ThinkingBoxContract.State()

        // Then
        assertEquals("", state.messageId)
        assertTrue(state.segmentsQueue.isEmpty())
        assertFalse(state.finalReady)
        assertEquals("", state.finalContent)
        assertFalse(state.thinkingClosed)
        assertEquals(null, state.error)
        assertFalse(state.isLoading)
    }

    @Test
    fun `should set messageId correctly`() {
        // Given
        val messageId = "test-message-123"

        // When
        val state = ThinkingBoxContract.State(messageId = messageId)

        // Then
        assertEquals(messageId, state.messageId)
        // 🔥 【Plan B重构】不再验证sessionId，通过ConversationIdManager获取
    }

    @Test
    fun `should set segments queue correctly`() {
        // Given
        val segments = listOf(
            ThinkingBoxContract.SegmentUi(
                id = "segment-1",
                kind = SegmentKind.PHASE,
                title = "思考阶段",
                content = "正在分析...",
                isComplete = true,
                isRendered = false
            )
        )

        // When
        val state = ThinkingBoxContract.State(segmentsQueue = segments)

        // Then
        assertEquals(1, state.segmentsQueue.size)
        assertEquals("segment-1", state.segmentsQueue[0].id)
        assertEquals(SegmentKind.PHASE, state.segmentsQueue[0].kind)
        assertEquals("思考阶段", state.segmentsQueue[0].title)
        assertEquals("正在分析...", state.segmentsQueue[0].content)
        assertTrue(state.segmentsQueue[0].isComplete)
        assertFalse(state.segmentsQueue[0].isRendered)
    }

    @Test
    fun `should set final content correctly`() {
        // Given
        val finalContent = "这是最终答案"

        // When
        val state = ThinkingBoxContract.State(
            finalReady = true,
            finalContent = finalContent,
            thinkingClosed = true
        )

        // Then
        assertTrue(state.finalReady)
        assertEquals(finalContent, state.finalContent)
        assertTrue(state.thinkingClosed)
    }

    @Test
    fun `should create Initialize intent correctly`() {
        // Given
        val messageId = "test-message-456"

        // When
        val intent = ThinkingBoxContract.Intent.Initialize(messageId)

        // Then
        assertEquals(messageId, intent.messageId)
        // 🔥 【Plan B重构】不再包含sessionId参数
    }

    @Test
    fun `should create UiSegmentRendered intent correctly`() {
        // Given
        val segmentId = "segment-123"

        // When
        val intent = ThinkingBoxContract.Intent.UiSegmentRendered(segmentId)

        // Then
        assertEquals(segmentId, intent.segmentId)
    }

    @Test
    fun `should create Reset and ClearError intents correctly`() {
        // When
        val resetIntent = ThinkingBoxContract.Intent.Reset
        val clearErrorIntent = ThinkingBoxContract.Intent.ClearError

        // Then
        assertEquals(ThinkingBoxContract.Intent.Reset, resetIntent)
        assertEquals(ThinkingBoxContract.Intent.ClearError, clearErrorIntent)
    }

    @Test
    fun `should create StartTokenStreamListening effect correctly`() {
        // Given
        val messageId = "test-message-789"

        // When
        val effect = ThinkingBoxContract.Effect.StartTokenStreamListening(messageId)

        // Then
        assertEquals(messageId, effect.messageId)
    }

    @Test
    fun `should create SegmentUi correctly`() {
        // Given
        val segmentUi = ThinkingBoxContract.SegmentUi(
            id = "test-segment",
            kind = SegmentKind.PHASE,
            title = "测试阶段",
            content = "测试内容",
            isComplete = true,
            isRendered = true
        )

        // Then
        assertEquals("test-segment", segmentUi.id)
        assertEquals(SegmentKind.PHASE, segmentUi.kind)
        assertEquals("测试阶段", segmentUi.title)
        assertEquals("测试内容", segmentUi.content)
        assertTrue(segmentUi.isComplete)
        assertTrue(segmentUi.isRendered)
    }
}
