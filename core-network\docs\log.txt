"model":"deepseek/deepseek-r1-...'
19:56:19.049 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=346->1
19:56:19.049 CNET-SERVICE-UnifiedAi   D  ✅ [Token处理成功] messageId=msg_CPPHVN, 输入长度=346 → 输出长度=1, 内容=' ...'
19:56:19.049 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=msg_CPPHVN, contentType=JSON_SSE, token=' ...'
19:56:19.050 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=msg_CPPHVN, token长度=1
19:56:19.050 CNET-SERVICE-UnifiedAi   V  📤 [Token发送确认] messageId=msg_CPPHVN, token=' ...', 长度=1
19:56:19.050 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#4950]
19:56:19.050 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
19:56:19.050 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#4951] data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-qwen3-8b:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"","reasoning":null,"reasoning_details":[]},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}
19:56:19.050 CNET-SERVICE-UnifiedAi   D  📡 [SSE数据#2476] 内容长度=339, 预览='{"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-q...'
19:56:19.050 CNET-SERVICE-UnifiedAi   V  📨 [原始Token接收] messageId=msg_CPPHVN, 长度=345, 预览='data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-...'
19:56:19.050 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=345->0
19:56:19.050 CNET-SERVICE-UnifiedAi   V  ⏭️ [Token跳过] messageId=msg_CPPHVN, 处理后为空，跳过发送
19:56:19.051 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#4952]
19:56:19.051 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
19:56:19.051 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#4953] data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-qwen3-8b:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"哪","reasoning":null,"reasoning_details":[]},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}
19:56:19.051 CNET-SERVICE-UnifiedAi   D  📡 [SSE数据#2477] 内容长度=340, 预览='{"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-q...'
19:56:19.051 CNET-SERVICE-UnifiedAi   V  📨 [原始Token接收] messageId=msg_CPPHVN, 长度=346, 预览='data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-...'
19:56:19.051 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=346->1
19:56:19.051 CNET-SERVICE-UnifiedAi   D  ✅ [Token处理成功] messageId=msg_CPPHVN, 输入长度=346 → 输出长度=1, 内容='哪...'
19:56:19.051 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=msg_CPPHVN, contentType=JSON_SSE, token='哪...'
19:56:19.051 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=msg_CPPHVN, token长度=1
19:56:19.051 CNET-SERVICE-UnifiedAi   V  📤 [Token发送确认] messageId=msg_CPPHVN, token='哪...', 长度=1
19:56:19.052 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#4954]
19:56:19.052 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
19:56:19.052 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#4955] data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-qwen3-8b:free","object":"chat.completion.chunk","created":**********,"choices":[{"index":0,"delta":{"role":"assistant","content":"个","reasoning":null,"reasoning_details":[]},"finish_reason":null,"native_finish_reason":null,"logprobs":null}]}
19:56:19.052 CNET-SERVICE-UnifiedAi   D  📡 [SSE数据#2478] 内容长度=340, 预览='{"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-0528-q...'
19:56:19.053 CNET-SERVICE-UnifiedAi   V  📨 [原始Token接收] messageId=msg_CPPHVN, 长度=346, 预览='data: {"id":"gen-**********-0wyKzeBj73toRpMEN5cB","provider":"Chutes","model":"deepseek/deepseek-r1-...'
19:56:19.053 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=346->1
19:56:19.053 CNET-SERVICE-UnifiedAi   D  ✅ [Token处理成功] messageId=msg_CPPHVN, 输入长度=346 → 输出长度=1, 内容='个...'
19:56:19.053 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=msg_CPPHVN, contentType=JSON_SSE, token='个...'
19:56:19.053 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=msg_CPPHVN, token长度=1
19:56:19.053 CNET-SERVICE-UnifiedAi   V  📤 [Token发送确认] messageId=msg_CPPHVN, token='个...', 长度=1
19:56:19.054 CNET-SERVICE-UnifiedAi   V  📡 [实时SSE行#4956]
19:56:19.054 CNET-SERVICE-UnifiedAi   V  📝 [SSE分隔符] 空行
19:56:19.160 CNET-TOKEN-OUT           I  📦 [msg_CPPHVN] ThinkingBox ← Batch[3 tokens, 6B, 3ms]
19:56:19.161 CNET-TOKEN-BATCH         D  📊 批量刷新完成: received=0, output=3
19:56:19.549 CNET-REST-Client         E  ❌ [真实流式请求失败] POST 错误=Software caused connection abort, 耗时=391432ms
java.net.SocketException: Software caused connection abort
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:994)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:958)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:873)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:846)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:153)
	at okio.RealBufferedSource.request(RealBufferedSource.kt:210)
	at okio.RealBufferedSource.require(RealBufferedSource.kt:203)
	at okhttp3.internal.http2.Http2Reader.nextFrame(Http2Reader.kt:89)
	at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:618)
	at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:609)
	at okhttp3.internal.concurrent.TaskQueue$execute$1.runOnce(TaskQueue.kt:98)
	at okhttp3.internal.concurrent.TaskRunner.runTask(TaskRunner.kt:116)
	at okhttp3.internal.concurrent.TaskRunner.access$runTask(TaskRunner.kt:42)
	at okhttp3.internal.concurrent.TaskRunner$runnable$1.run(TaskRunner.kt:65)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
	at java.lang.Thread.run(Thread.java:1119)
19:56:19.550 CNET-SERVICE-UnifiedAi   E  ❌ [流式请求异常] Software caused connection abort
java.net.SocketException: Software caused connection abort
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:994)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:958)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:873)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:846)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:153)
	at okio.RealBufferedSource.request(RealBufferedSource.kt:210)
	at okio.RealBufferedSource.require(RealBufferedSource.kt:203)
	at okhttp3.internal.http2.Http2Reader.nextFrame(Http2Reader.kt:89)
	at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:618)
	at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:609)
	at okhttp3.internal.concurrent.TaskQueue$execute$1.runOnce(TaskQueue.kt:98)
	at okhttp3.internal.concurrent.TaskRunner.runTask(TaskRunner.kt:116)
	at okhttp3.internal.concurrent.TaskRunner.access$runTask(TaskRunner.kt:42)
	at okhttp3.internal.concurrent.TaskRunner$runnable$1.run(TaskRunner.kt:65)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
	at java.lang.Thread.run(Thread.java:1119)
19:56:19.550 CNET-SERVICE-UnifiedAi   E  ❌ [请求失败] messageId=msg_CPPHVN, tokens=2033, 耗时=391634ms, 错误=Software caused connection abort
java.net.SocketException: Software caused connection abort
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:994)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:958)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:873)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:846)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:153)
	at okio.RealBufferedSource.request(RealBufferedSource.kt:210)
	at okio.RealBufferedSource.require(RealBufferedSource.kt:203)
	at okhttp3.internal.http2.Http2Reader.nextFrame(Http2Reader.kt:89)
	at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:618)
	at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:609)
	at okhttp3.internal.concurrent.TaskQueue$execute$1.runOnce(TaskQueue.kt:98)
	at okhttp3.internal.concurrent.TaskRunner.runTask(TaskRunner.kt:116)
	at okhttp3.internal.concurrent.TaskRunner.access$runTask(TaskRunner.kt:42)
	at okhttp3.internal.concurrent.TaskRunner$runnable$1.run(TaskRunner.kt:65)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
	at java.lang.Thread.run(Thread.java:1119)
19:56:19.550 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=msg_CPPHVN, contentType=PLAIN_TEXT, token='AI响应处理失败: Software caused connection abort...'
19:56:19.551 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=msg_CPPHVN, token长度=42
19:56:19.551 AI-STREAM-REPO           E  ❌ AI流启动失败: messageId=msg_CPPHVN
java.net.SocketException: Software caused connection abort
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:994)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:958)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:873)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:846)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:153)
	at okio.RealBufferedSource.request(RealBufferedSource.kt:210)
	at okio.RealBufferedSource.require(RealBufferedSource.kt:203)
	at okhttp3.internal.http2.Http2Reader.nextFrame(Http2Reader.kt:89)
	at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:618)
	at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:609)
	at okhttp3.internal.concurrent.TaskQueue$execute$1.runOnce(TaskQueue.kt:98)
	at okhttp3.internal.concurrent.TaskRunner.runTask(TaskRunner.kt:116)
	at okhttp3.internal.concurrent.TaskRunner.access$runTask(TaskRunner.kt:42)
	at okhttp3.internal.concurrent.TaskRunner$runnable$1.run(TaskRunner.kt:65)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
	at java.lang.Thread.run(Thread.java:1119)
19:56:19.563 AndroidRuntime           E  FATAL EXCEPTION: main
Process: com.example.gymbro, PID: 7178
java.net.SocketException: Software caused connection abort
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
	at java.net.SocketInputStream.read(SocketInputStream.java:173)
	at java.net.SocketInputStream.read(SocketInputStream.java:143)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:994)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:958)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:873)
	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:846)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:153)
	at okio.RealBufferedSource.request(RealBufferedSource.kt:210)
	at okio.RealBufferedSource.require(RealBufferedSource.kt:203)
	at okhttp3.internal.http2.Http2Reader.nextFrame(Http2Reader.kt:89)
	at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:618)
	at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:609)
	at okhttp3.internal.concurrent.TaskQueue$execute$1.runOnce(TaskQueue.kt:98)
	at okhttp3.internal.concurrent.TaskRunner.runTask(TaskRunner.kt:116)
	at okhttp3.internal.concurrent.TaskRunner.access$runTask(TaskRunner.kt:42)
	at okhttp3.internal.concurrent.TaskRunner$runnable$1.run(TaskRunner.kt:65)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
	at java.lang.Thread.run(Thread.java:1119)
	Suppressed: kotlinx.coroutines.internal.DiagnosticCoroutineContextException: [CoroutineName(MviEffectScope-AiCoachViewModel), StandaloneCoroutine{Cancelling}@ae0ce0f, Dispatchers.Main.immediate]
19:56:19.589 Process                  I  Sending signal. PID: 7178 SIG: 9
