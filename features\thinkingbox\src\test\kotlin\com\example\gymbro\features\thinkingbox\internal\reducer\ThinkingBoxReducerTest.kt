package com.example.gymbro.features.thinkingbox.internal.reducer

import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * ThinkingBoxReducer测试 - Plan B重构版本
 *
 * 🔥 【Plan B重构】验证主Reducer逻辑，确保sessionId移除后的功能完整性
 */
class ThinkingBoxReducerTest {

    private lateinit var reducer: ThinkingBoxReducer

    @Before
    fun setup() {
        reducer = ThinkingBoxReducer(
            segmentQueueReducer = SegmentQueueReducer()
        )
    }

    @Test
    fun `should handle Initialize intent correctly`() {
        // Given
        val intent = ThinkingBoxContract.Intent.Initialize("test-message-123")
        val currentState = ThinkingBoxContract.State()

        // When
        val result = reducer.reduce(intent, currentState)

        // Then
        assertEquals("test-message-123", result.newState.messageId)
        // 🔥 【Plan B重构】不再验证sessionId，通过ConversationIdManager获取
        assertFalse(result.newState.isLoading)

        // 验证Effect
        assertEquals(1, result.effects.size)
        assertTrue(result.effects[0] is ThinkingBoxContract.Effect.StartTokenStreamListening)
        val effect = result.effects[0] as ThinkingBoxContract.Effect.StartTokenStreamListening
        assertEquals("test-message-123", effect.messageId)
    }

    @Test
    fun `should handle Reset intent correctly`() {
        // Given
        val intent = ThinkingBoxContract.Intent.Reset
        val currentState = ThinkingBoxContract.State(
            messageId = "existing-message",
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    id = "segment-1",
                    kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.PHASE,
                    title = "思考",
                    content = "内容",
                    isComplete = true,
                    isRendered = false
                )
            ),
            finalReady = true,
            finalContent = "最终内容",
            thinkingClosed = true
        )

        // When
        val result = reducer.reduce(intent, currentState)

        // Then
        assertEquals("", result.newState.messageId)
        assertTrue(result.newState.segmentsQueue.isEmpty())
        assertFalse(result.newState.finalReady)
        assertEquals("", result.newState.finalContent)
        assertFalse(result.newState.thinkingClosed)
        assertFalse(result.newState.isLoading)
        assertEquals(null, result.newState.error)
    }

    @Test
    fun `should handle UiSegmentRendered intent correctly`() {
        // Given
        val intent = ThinkingBoxContract.Intent.UiSegmentRendered("segment-123")
        val currentState = ThinkingBoxContract.State(
            messageId = "test-message",
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    id = "segment-123",
                    kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.PHASE,
                    title = "思考",
                    content = "内容",
                    isComplete = true,
                    isRendered = false
                )
            )
        )

        // When
        val result = reducer.reduce(intent, currentState)

        // Then
        assertEquals(1, result.state.segmentsQueue.size)
        val updatedSegment = result.state.segmentsQueue[0]
        assertEquals("segment-123", updatedSegment.id)
        assertTrue(updatedSegment.isRendered)

        // 验证其他属性保持不变
        assertEquals("思考", updatedSegment.title)
        assertEquals("内容", updatedSegment.content)
        assertTrue(updatedSegment.isComplete)
    }

    @Test
    fun `should not affect state when UiSegmentRendered for non-existent segment`() {
        // Given
        val intent = ThinkingBoxContract.Intent.UiSegmentRendered("non-existent-segment")
        val currentState = ThinkingBoxContract.State(
            messageId = "test-message",
            segmentsQueue = listOf(
                ThinkingBoxContract.SegmentUi(
                    id = "segment-123",
                    kind = com.example.gymbro.features.thinkingbox.domain.model.SegmentKind.THINKING,
                    title = "思考",
                    content = "内容",
                    isComplete = true,
                    isRendered = false
                )
            )
        )

        // When
        val result = reducer.reduce(intent, currentState)

        // Then
        assertEquals(currentState, result.state)
        assertTrue(result.effects.isEmpty())
    }

    @Test
    fun `should handle ClearError intent correctly`() {
        // Given
        val intent = ThinkingBoxContract.Intent.ClearError
        val currentState = ThinkingBoxContract.State(
            messageId = "test-message",
            error = com.example.gymbro.core.ui.text.UiText.DynamicString("测试错误")
        )

        // When
        val result = reducer.reduce(intent, currentState)

        // Then
        assertEquals(null, result.state.error)
        assertEquals("test-message", result.state.messageId) // 其他状态保持不变
    }
}
