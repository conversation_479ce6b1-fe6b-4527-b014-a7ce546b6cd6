package com.example.gymbro.features.thinkingbox.api

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.gymbro.features.thinkingbox.ThinkingBoxInternal
import com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel.ThinkingBoxViewModel
import kotlinx.coroutines.flow.Flow
import timber.log.Timber

/**
 * ThinkingBoxWithCallback - 支持完成回调的ThinkingBox组件
 *
 * 🔥 【Plan B重构】新的统一公共API：
 * - 符合"ThinkingBox只负责接收token流"的架构要求
 * - 统一使用messageId，通过ConversationIdManager获取sessionId
 * - 支持ModernResult<T>错误处理机制
 * - 自动管理ViewModel的回调设置
 * - 保持向后兼容性
 *
 * 使用场景：
 * ```kotlin
 * ThinkingBoxWithCallback(
 *     messageId = "ai-response-123",
 *     completionListener = coachCompletionListener,
 *     modifier = Modifier.fillMaxWidth()
 * )
 * ```
 *
 * 🔥 【架构优势】：
 * - 专注于显示控制，不涉及AI请求发送
 * - 消除ID传递的复杂性
 * - 统一的错误处理和状态管理
 * - 符合Plan B架构要求
 */
@Composable
fun ThinkingBoxWithCallback(
    messageId: String,
    completionListener: ThinkingBoxCompletionListener? = null,
    modifier: Modifier = Modifier,
    tokenFlow: Flow<String>? = null,
    viewModel: ThinkingBoxViewModel = hiltViewModel(),
) {
    // 🔥 【Plan B重构】设置完成回调监听器，简化参数传递
    LaunchedEffect(messageId, completionListener) {
        if (completionListener != null) {
            Timber.i("TB-API: 🔗 【Plan B重构】设置ThinkingBox完成回调: messageId=$messageId")

            try {
                // 🔥 【架构修复】设置完成回调监听器到ViewModel
                viewModel.setCompletionListener(completionListener)

                // 初始化ThinkingBox
                viewModel.initialize(messageId)
                Timber.d("TB-API: ✅ 【Plan B重构】ThinkingBox初始化完成")
            } catch (e: Exception) {
                Timber.e(e, "TB-API: ❌ 【Plan B重构】ThinkingBox初始化失败")
            }
        } else {
            Timber.d("TB-API: ⚠️ 未设置完成回调监听器: messageId=$messageId")
            // 无回调监听器时仍然初始化ViewModel
            viewModel.initialize(messageId)
        }
    }

    // 🔥 【兼容性】使用现有的ThinkingBoxInternal组件
    ThinkingBoxInternal(
        messageId = messageId,
        modifier = modifier,
        tokenFlow = tokenFlow,
        viewModel = viewModel,
    )
}

/**
 * ThinkingBoxWithCallback - 简化版本（无tokenFlow）
 *
 * 最常用的API，适用于大多数场景
 */
@Composable
fun ThinkingBoxWithCallback(
    messageId: String,
    completionListener: ThinkingBoxCompletionListener? = null,
    modifier: Modifier = Modifier,
) {
    ThinkingBoxWithCallback(
        messageId = messageId,
        completionListener = completionListener,
        modifier = modifier,
        tokenFlow = null,
    )
}

/**
 * 🔥 【向后兼容】ThinkingBox别名
 *
 * 保持与现有代码的兼容性，逐步迁移到新API
 */
@Composable
fun ThinkingBox(
    messageId: String,
    modifier: Modifier = Modifier,
    tokenFlow: Flow<String>? = null,
) {
    Timber.d("TB-API: 📢 使用兼容模式ThinkingBox: messageId=$messageId")

    ThinkingBoxInternal(
        messageId = messageId,
        modifier = modifier,
        tokenFlow = tokenFlow,
    )
}
