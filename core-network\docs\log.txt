2025-08-04 19:19:28.747 WM-WorkerWrapper         D  Starting work for com.example.gymbro.features.coach.shared.managers.HardwareDetectionWorker
2025-08-04 19:19:28.749 WM-SystemJobService      D  onStartJob for WorkGenerationalId(workSpecId=8034a487-d968-49f3-9fd0-583fbafaf9a0, generation=0)
2025-08-04 19:19:28.752 WM-Processor             D  Work WorkGenerationalId(workSpecId=8034a487-d968-49f3-9fd0-583fbafaf9a0, generation=0) is already enqueued for processing
2025-08-04 19:19:28.753 WM-WorkerWrapper         D  com.example.gymbro.features.coach.shared.managers.HardwareDetectionWorker returned a Success {mOutputData=Data {}}.
2025-08-04 19:19:28.754 WM-WorkerWrapper         I  Worker result SUCCESS for Work [ id=8034a487-d968-49f3-9fd0-583fbafaf9a0, tags={ com.example.gymbro.features.coach.shared.managers.HardwareDetectionWorker } ]
2025-08-04 19:19:28.761 Choreographer            I  Skipped 76 frames!  The application may be doing too much work on its main thread.
2025-08-04 19:19:28.768 WM-PackageManagerHelper  D  androidx.work.impl.background.systemalarm.RescheduleReceiver disabled
2025-08-04 19:19:28.796 WM-Processor             D  Processor 8034a487-d968-49f3-9fd0-583fbafaf9a0 executed; reschedule = false
2025-08-04 19:19:28.798 WM-GreedyScheduler       D  Stopping tracking for WorkGenerationalId(workSpecId=8034a487-d968-49f3-9fd0-583fbafaf9a0, generation=0)
2025-08-04 19:19:28.805 WM-SystemJobService      D  8034a487-d968-49f3-9fd0-583fbafaf9a0 executed on JobScheduler
2025-08-04 19:19:28.865 WM-GreedyScheduler       D  Cancelling work ID 8034a487-d968-49f3-9fd0-583fbafaf9a0
2025-08-04 19:19:28.877 WM-BrdcstRcvrCnstrntTrc  D  BatteryNotLowTracker: unregistering receiver
2025-08-04 19:19:28.880 WM-BrdcstRcvrCnstrntTrc  D  StorageNotLowTracker: unregistering receiver
2025-08-04 19:19:28.900 WM-SystemJobService      D  onStopJob for WorkGenerationalId(workSpecId=8034a487-d968-49f3-9fd0-583fbafaf9a0, generation=0)
2025-08-04 19:19:28.962 InsetsController         D  hide(ime(), fromIme=false)
2025-08-04 19:19:28.964 ImeTracker               I  com.example.gymbro:144b6209: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-08-04 19:19:30.148 Choreographer            I  Skipped 63 frames!  The application may be doing too much work on its main thread.
2025-08-04 19:19:30.170 okhttp.OkHttpClient      I  <-- HTTP FAILED: java.net.SocketException: Connection reset
2025-08-04 19:19:30.273 RegionDete...Repository  W  🌐 网络连接重置，可能是网络不稳定或服务端限制
2025-08-04 19:19:30.273 RegionDete...Repository  W  java.net.SocketException: Connection reset
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at java.net.SocketInputStream.read(SocketInputStream.java:191)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at java.net.SocketInputStream.read(SocketInputStream.java:143)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:994)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:958)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.-$$Nest$mprocessDataFromSocket(Unknown Source:0)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at com.android.org.conscrypt.ConscryptEngineSocket.doHandshake(ConscryptEngineSocket.java:242)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at com.android.org.conscrypt.ConscryptEngineSocket.startHandshake(ConscryptEngineSocket.java:224)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
2025-08-04 19:19:30.273 RegionDete...Repository  W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at okhttp3.logging.HttpLoggingInterceptor.intercept(HttpLoggingInterceptor.kt:221)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	at java.lang.Thread.run(Thread.java:1119)
2025-08-04 19:19:30.274 RegionDete...Repository  W  	Suppressed: java.net.ConnectException: Failed to connect to api.ipify.org/*************:443
2025-08-04 19:19:30.274 RegionDete...Repository  W  		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:297)
2025-08-04 19:19:30.274 RegionDete...Repository  W  		at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:207)
2025-08-04 19:19:30.274 RegionDete...Repository  W  		... 19 more
2025-08-04 19:19:30.274 RegionDete...Repository  W  	Caused by: java.net.ConnectException: failed to connect to api.ipify.org/************* (port 443) from /********* (port 38588) after 3000ms: isConnected failed: ECONNRESET (Connection reset by peer)
2025-08-04 19:19:30.274 RegionDete...Repository  W  		at libcore.io.IoBridge.isConnected(IoBridge.java:347)
2025-08-04 19:19:30.274 RegionDete...Repository  W  		at libcore.io.IoBridge.connectErrno(IoBridge.java:237)
2025-08-04 19:19:30.274 RegionDete...Repository  W  		at libcore.io.IoBridge.connect(IoBridge.java:179)
2025-08-04 19:19:30.274 RegionDete...Repository  W  		at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
2025-08-04 19:19:30.274 RegionDete...Repository  W  		at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
2025-08-04 19:19:30.275 RegionDete...Repository  W  		at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
2025-08-04 19:19:30.275 RegionDete...Repository  W  		at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
2025-08-04 19:19:30.275 RegionDete...Repository  W  		at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
2025-08-04 19:19:30.275 RegionDete...Repository  W  		at java.net.Socket.connect(Socket.java:646)
2025-08-04 19:19:30.275 RegionDete...Repository  W  		at okhttp3.internal.platform.Platform.connectSocket(Platform.kt:128)
2025-08-04 19:19:30.275 RegionDete...Repository  W  		at okhttp3.internal.connection.RealConnection.connectSocket(RealConnection.kt:295)
2025-08-04 19:19:30.275 RegionDete...Repository  W  		... 20 more
2025-08-04 19:19:30.275 RegionDete...Repository  W  	Caused by: android.system.ErrnoException: isConnected failed: ECONNRESET (Connection reset by peer)
2025-08-04 19:19:30.275 RegionDete...Repository  W  		at libcore.io.IoBridge.isConnected(IoBridge.java:334)
2025-08-04 19:19:30.275 RegionDete...Repository  W  		... 30 more
2025-08-04 19:19:30.275 RegionDete...Repository  W  	Suppressed: java.net.SocketException: Connection reset
2025-08-04 19:19:30.275 RegionDete...Repository  W  		... 29 more
2025-08-04 19:19:30.276 RegionDete...Repository  W  无法获取当前IP，返回默认国际区域
2025-08-04 19:19:30.276 RegionDetectionManager   I  地区检测完成: INTERNATIONAL, 方法: IP_GEOLOCATION, 置信度: 0.9
2025-08-04 19:19:32.312 ProfileInstaller         D  Installing profile for com.example.gymbro
2025-08-04 19:19:36.170 PromptModeManager        I  🔧 PromptModeManager初始化完成，统一配置管理架构就绪
2025-08-04 19:19:36.190 CNET-REST-Client         D  🔥 RestClient初始化 (工业级动态配置版本):
2025-08-04 19:19:36.199 CNET-TOKEN-STATS         I  🚀 TokenLogCollector已启动，定时刷新间隔=50ms
2025-08-04 19:19:36.213 CNET-DI-Module           D  🔧 [组件创建] UnifiedAiResponseService - 统一AI响应服务
2025-08-04 19:19:36.868 AppStartup               I  🎯 用户交互窗口检查 (10013ms):
2025-08-04 19:19:36.869 AppStartup               I    - 网络就绪: true
2025-08-04 19:19:36.869 AppStartup               I    - AI核心就绪: true
2025-08-04 19:19:36.869 AppStartup               I    - 整体进度: 100%
2025-08-04 19:19:36.869 AppStartup               I  🎉 优秀！10013ms内完成所有组件初始化
2025-08-04 19:19:37.627 CompatChangeReporter     D  Compat change id reported: 289878283; UID 10215; state: ENABLED
2025-08-04 19:19:38.258 Choreographer            I  Skipped 133 frames!  The application may be doing too much work on its main thread.
2025-08-04 19:19:40.689 COACH-MESSAGE-FLOW       I  🚀 [UI发送] 用户点击发送按钮，消息内容: 我想开始力量训练，我应该从哪里开始？
2025-08-04 19:19:40.689 COACH-MESSAGE-FLOW       I  🚀 [UI发送] 发送状态检查: canSendMessage=true, isLoading=false
2025-08-04 19:19:40.695 MessagingReducerHandler  I  🆕 创建用户消息: WALKHC in session msg_WPXRXB
2025-08-04 19:19:40.695 MessagingReducerHandler  I  🆕 创建AI消息: 22SWLC in session msg_WPXRXB
2025-08-04 19:19:40.704 MESSAGE-SAVE             I  🎯 [AiCoachEffectHandler] 路由SaveUserMessage到AiCoachSessionHandler: messageId=msg_3GVBX6, sessionId=msg_WPXRXB
2025-08-04 19:19:40.705 COACH-MESSAGE-FLOW       I  🚀 [EFFECT处理] StartAiStream Effect收到: messageContext=MessageContext(compactId=22SWLC, messageId=msg_QNJE..., sessionId=msg_WPXRXB)
2025-08-04 19:19:40.714 COACH-PLAN-B             I  🚀 【Plan B架构】Coach发送AI请求: MessageContext(compactId=22SWLC, messageId=msg_QNJE..., sessionId=msg_WPXRXB)
2025-08-04 19:19:40.714 COACH-PLAN-B             I  📤 Coach发送AI请求到供应商: messageId=msg_QNJEM7
2025-08-04 19:19:40.715 BgeMemoryMonitor         W  ⚠️ BGE可用内存偏低: 22MB < 120MB (警戒线)
2025-08-04 19:19:40.717 tflite                   I  Replacing 200 out of 391 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 76 partitions for subgraph 0.
2025-08-04 19:19:40.719 COACH-PLAN-B             I  ✅ AI请求发送成功，ThinkingBox将被动响应
2025-08-04 19:19:40.725 CNET-SERVICE-UnifiedAi   I  🚀 [请求开始] messageId=msg_QNJEM7, 开始统一AI响应处理
2025-08-04 19:19:40.726 CNET-SERVICE-UnifiedAi   D  🔍 [数据流] Core-Network接收: messageId=msg_QNJEM7
2025-08-04 19:19:40.731 Coach-ThinkingBox        D  🔍 [显示决策] isStreaming=true
2025-08-04 19:19:40.736 CNET-SERVICE-UnifiedAi   D  📡 [流式AI请求] 发送到: https://catsapi.com/v1/chat/completions
2025-08-04 19:19:40.739 CNET-REST-Client         I  🌊 [真实流式请求开始] POST https://catsapi.com/v1/chat/completions, 总请求数=1
2025-08-04 19:19:40.760 TB-COACH                 I  🔍 AwaitingFirstToken状态: 实际AI消息ID=msg_QNJEM7
2025-08-04 19:19:40.789 TB-VM                    D  🚀 [ViewModel初始化] ThinkingBoxViewModel启动
2025-08-04 19:19:40.870 TB-COACH                 I  🚀 ThinkingBox激活: messageId=msg_QNJEM7, state=AwaitingFirstToken
2025-08-04 19:19:40.871 TB-Thinkin...thCallback  I  TB-API: 🔗 【Plan B重构】设置ThinkingBox完成回调: messageId=msg_QNJEM7
2025-08-04 19:19:40.872 TB-REDUCER-Segment       D  🔄 [Intent处理] Initialize
2025-08-04 19:19:40.872 TB-VM-Main               D  🔍 [数据流] ViewModel激活: messageId=msg_QNJEM7
2025-08-04 19:19:40.872 TB-INIT                  I  🚀 [初始化] ThinkingBox: messageId=msg_QNJEM7
2025-08-04 19:19:40.918 TB-EFFECT                D  ⚡ [Effect处理] StartTokenStreamListening
2025-08-04 19:19:40.918 TB-STREAM                I  🎯 [Token流启动] messageId=msg_QNJEM7
2025-08-04 19:19:40.919 TB-STREAM                I  ✅ [直接订阅] DirectOutputChannel: messageId=msg_QNJEM7
2025-08-04 19:19:40.919 CNET-OUTPUT-Direct       D  🔗 [订阅] messageId=msg_QNJEM7, 活跃订阅者=1
2025-08-04 19:19:40.922 TB-PARSER-Stream         I  🚀 [解析启动] 开始解析token流: messageId=msg_QNJEM7
2025-08-04 19:19:40.925 TB-Thinkin...thCallback  D  TB-API: ✅ 【Plan B重构】ThinkingBox初始化完成
2025-08-04 19:19:40.926 TB-Thinkin...oxInternal  D  TB-VM:  🚀 初始化ThinkingBox: msg_QNJEM7, hasTokenFlow=false
2025-08-04 19:19:40.926 TB-Thinkin...elProvider  D  TB-Provider:TB-Provider: 🔗 [ThinkingBoxViewModelProvider] 注册ViewModel: messageId=msg_QNJEM7
2025-08-04 19:19:40.927 TB-REDUCER-Segment       D  🔄 [Intent处理] Initialize
2025-08-04 19:19:40.927 TB-VM-Main               D  🔍 [数据流] ViewModel激活: messageId=msg_QNJEM7
2025-08-04 19:19:40.927 TB-INIT                  I  🚀 [初始化] ThinkingBox: messageId=msg_QNJEM7
2025-08-04 19:19:40.927 TB-INIT                  D  ⏭️ [跳过重复] 初始化: msg_QNJEM7
2025-08-04 19:19:40.927 TB-Thinkin...oxInternal  D  TB-VM:  🔗 连接HistoryActor到Effect流: msg_QNJEM7
2025-08-04 19:19:40.930 TB-HistoryActor          I  TB-History: 🚀 [初始化] 开始监听ThinkingBox Effect流
2025-08-04 19:19:40.963 Coach-ThinkingBox        D  🔍 [显示决策] isStreaming=true
2025-08-04 19:19:40.969 TB-COACH                 I  🔍 AwaitingFirstToken状态: 实际AI消息ID=msg_QNJEM7
2025-08-04 19:19:40.993 Coach-ThinkingBox        D  🔍 [显示决策] isStreaming=true
