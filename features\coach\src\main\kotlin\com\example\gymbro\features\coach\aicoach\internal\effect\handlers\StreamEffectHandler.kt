package com.example.gymbro.features.coach.aicoach.internal.effect.handlers

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.AiTaskType
import com.example.gymbro.domain.coach.repository.AiStreamRepository
import com.example.gymbro.domain.coach.usecase.DetectActionableContentUseCase
import com.example.gymbro.domain.coach.usecase.HistoryContextBridgeUseCase
import com.example.gymbro.domain.coach.usecase.PrepareAiContextUseCase
import com.example.gymbro.domain.coach.usecase.SendChatMessageAndGetResponseUseCase
import com.example.gymbro.domain.workout.usecase.GenerateStructuredPlanUseCase
import com.example.gymbro.domain.workout.usecase.template.TemplateGenerationUseCase
import com.example.gymbro.features.coach.aicoach.AiCoachContract

import com.example.gymbro.shared.models.ai.ChatMessage
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * StreamEffectHandler - AI请求发送处理器 (Plan B重构版)
 *
 * 🔥 【Plan B架构】核心原则：
 * - Coach只负责构建和发送AI请求，不处理响应
 * - ThinkingBox被动响应AI回应，不由Coach主动启动
 * - Coach发送"用户已发送消息"状态，激活ThinkingBox的ThinkingHeader显示条件
 * - Token流完全由Core-Network → DirectOutputChannel → ThinkingBox自动处理
 *
 * 核心职责：AI请求构建和发送、Function Call检测、状态通知
 */
internal class StreamEffectHandler
@Inject
constructor(
    private val prepareAiContextUseCase: PrepareAiContextUseCase,
    private val detectActionableContentUseCase: DetectActionableContentUseCase,
    private val historyContextBridgeUseCase: HistoryContextBridgeUseCase,
    private val templateGenerationUseCase: TemplateGenerationUseCase,
    private val generateStructuredPlanUseCase: GenerateStructuredPlanUseCase,
    private val aiStreamRepository: AiStreamRepository,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) {
    private lateinit var handlerScope: CoroutineScope
    private lateinit var sendIntent: (AiCoachContract.Intent) -> Unit
    private lateinit var stateProvider: () -> AiCoachContract.State

    private var currentStreamJob: Job? = null

    fun initialize(
        scope: CoroutineScope,
        intentSender: (AiCoachContract.Intent) -> Unit,
        stateProvider: () -> AiCoachContract.State,
    ) {
        this.handlerScope = scope
        this.sendIntent = intentSender
        this.stateProvider = stateProvider
    }

    fun handleStartAiStream(effect: AiCoachContract.Effect.StartAiStream) {
        currentStreamJob?.cancel()

        currentStreamJob = handlerScope.launch {
            try {
                // 参数验证
                if (effect.messageContext.sessionId.isBlank() || effect.messageContext.messageId.isBlank() || effect.prompt.isBlank()) {
                    sendIntent(AiCoachContract.Intent.ShowError("请求参数无效"))
                    return@launch
                }

                Timber.tag("COACH-PLAN-B").i("🚀 【Plan B架构】Coach发送AI请求: ${effect.messageContext.getDebugInfo()}")

                // 🔥 【Plan B架构】正确的数据流：
                // 1. Coach构建并发送AI请求
                // 2. Coach发送"用户已发送消息"状态，激活ThinkingBox的ThinkingHeader
                // 3. AI响应通过Core-Network → DirectOutputChannel → ThinkingBox被动显示
                // 4. ThinkingBox完成后回调Coach保存结果

                Timber.tag("COACH-PLAN-B").i("📤 Coach发送AI请求到供应商: messageId=${effect.messageContext.messageId}")

                try {
                    // 🔥 【修复】调用SendChatMessageAndGetResponseUseCase使用LayeredPromptBuilder
                    Timber.d("🚀 [StreamEffectHandler] 调用SendChatMessageAndGetResponseUseCase构建完整prompt")

                    // 🔥 【关键修复】使用SendChatMessageAndGetResponseUseCase，确保LayeredPromptBuilder被调用
                    val params = SendChatMessageAndGetResponseUseCase.Params(
                        sessionId = effect.messageContext.sessionId,
                        userMessageContent = effect.prompt,
                        currentModel = "deepseek-chat", // 传递模型信息
                        forceOmitSystemPrompt = false
                    )

                    // 🔥 【架构重构】通过UseCase发送请求，确保完整的prompt构建流程
                    handlerScope.launch {
                        try {
                            sendChatMessageAndGetResponseUseCase(params).collect { result ->
                                when (result) {
                                    is ModernResult.Success -> {
                                        // 🔥 【职责分离】Coach不处理token，只确保流启动
                                        // Token会自动通过DirectOutputChannel路由到ThinkingBox
                                        Timber.tag("COACH-PLAN-B").v("📥 收到token: ${result.data.take(50)}...")
                                    }
                                    is ModernResult.Error -> {
                                        Timber.tag("COACH-PLAN-B").e("❌ AI响应错误: ${result.error}")
                                        sendIntent(AiCoachContract.Intent.ShowError("AI响应错误: ${result.error}"))
                                    }
                                    is ModernResult.Loading -> {
                                        // 忽略Loading状态
                                    }
                                }
                            }

                            Timber.tag("COACH-PLAN-B").i("🚀 AI请求流已启动，等待ThinkingBox完成回调")
                        } catch (e: Exception) {
                            Timber.tag("COACH-PLAN-B").e(e, "❌ AI请求发送失败")
                            throw e
                        }
                    }

                    // 🔥 【Plan B架构】发送"用户已发送消息"状态，激活ThinkingBox的ThinkingHeader
                    sendIntent(
                        AiCoachContract.Intent.UserMessageSent(
                            messageId = effect.messageContext.messageId,
                            sessionId = effect.messageContext.sessionId,
                        )
                    )

                    Timber.tag("COACH-PLAN-B").i("✅ AI请求发送成功，ThinkingBox将被动响应")
                } catch (e: Exception) {
                    Timber.tag("COACH-PLAN-B").e(e, "❌ 发送AI请求失败: messageId=${effect.messageContext.messageId}")
                    sendIntent(AiCoachContract.Intent.ShowError("发送消息失败: ${e.message}"))
                }
            } catch (e: Exception) {
                Timber.tag("COACH-PLAN-B").e(e, "❌ AI请求发送异常")
                sendIntent(AiCoachContract.Intent.ResetStreamingState)
                sendIntent(AiCoachContract.Intent.ShowError("AI请求发送异常: ${e.message}"))
            }
        }
    }



    private suspend fun prepareEnhancedPrompt(effect: AiCoachContract.Effect.StartAiStream): String {
        // 1. 尝试使用History RAG上下文
        val historyResult = historyContextBridgeUseCase(
            sessionId = effect.messageContext.sessionId,
        )

        if (historyResult is ModernResult.Success) {
            // HistoryRagContext包含summary，我们需要将其与用户prompt结合
            val historyContext = historyResult.data
            return buildPromptWithHistoryContext(effect.prompt, historyContext)
        }

        // 2. 降级到标准RAG上下文准备
        val contextResult = prepareAiContextUseCase(
            query = effect.prompt,
            sessionId = effect.messageContext.sessionId,
        )

        return when (contextResult) {
            is ModernResult.Success -> {
                val context = contextResult.data

                // 触发用户资料检查
                sendIntent(AiCoachContract.Intent.CheckProfileCompletion(context.userProfile.toString()))

                // 使用PromptModeManager构建完整prompt
                buildPromptWithContext(effect.prompt, context)
            }
            is ModernResult.Error -> {
                Timber.w("RAG上下文准备失败: ${contextResult.error}")
                buildBasicPrompt(effect.prompt)
            }
            is ModernResult.Loading -> effect.prompt
        }
    }

    private suspend fun buildPromptWithHistoryContext(
        userPrompt: String,
        historyContext: com.example.gymbro.core.ai.prompt.model.HistoryRagContext,
    ): String {
        // 简化的prompt构建，直接组合用户输入和历史摘要
        return buildString {
            append("[SYSTEM]\n")
            append("你是一个专业的健身教练AI助手。请基于用户的历史对话背景提供个性化建议。\n\n")

            append("[CONTEXT]\n")
            append("用户历史对话摘要：\n${historyContext.summary}\n\n")

            append("[USER]\n")
            append(userPrompt)
        }
    }

    private suspend fun buildPromptWithContext(
        userPrompt: String,
        context: com.example.gymbro.shared.models.ai.RelevantAiContext,
    ): String {
        // 简化的prompt构建，直接组合用户输入和上下文
        return buildString {
            append("[SYSTEM]\n")
            append("你是一个专业的健身教练AI助手。请基于用户资料和相关上下文提供个性化建议。\n\n")

            append("[CONTEXT]\n")
            append("用户资料：${context.userProfile}\n")
            if (context.recentHistory.isNotEmpty()) {
                append("最近对话：\n")
                context.recentHistory.takeLast(3).forEach { msg ->
                    append("- ${msg.sender}: ${msg.content}\n")
                }
            }
            append("\n")

            append("[USER]\n")
            append(userPrompt)
        }
    }

    private fun buildContextHistory(context: com.example.gymbro.shared.models.ai.RelevantAiContext): List<com.example.gymbro.core.ai.prompt.builder.ConversationTurn> {
        val contextTurns = mutableListOf<com.example.gymbro.core.ai.prompt.builder.ConversationTurn>()

        // 添加用户资料和模板上下文作为一个对话轮次
        val contextInfo = buildString {
            if (context.userProfile.gender != "未知性别" || context.userProfile.age != 25) {
                append(buildProfileContext(context.userProfile))
                append("\n")
            }
            if (context.relevantTemplates.isNotEmpty()) {
                append(buildTemplatesContext(context.relevantTemplates))
            }
        }

        if (contextInfo.isNotBlank()) {
            contextTurns.add(
                com.example.gymbro.core.ai.prompt.builder.ConversationTurn(
                    user = "请了解我的基本信息和相关训练背景",
                    assistant = "好的，我已了解您的信息：\n$contextInfo",
                ),
            )
        }

        // 添加最近对话历史（转换为ConversationTurn格式）
        val recentMessages = context.recentHistory.takeLast(4) // 取最近4条消息，组成2个对话轮次
        var i = 0
        while (i < recentMessages.size - 1) {
            val currentMessage = recentMessages[i]
            val nextMessage = recentMessages[i + 1]

            if (currentMessage.sender == "user" && nextMessage.sender != "user") {
                contextTurns.add(
                    com.example.gymbro.core.ai.prompt.builder.ConversationTurn(
                        user = currentMessage.content,
                        assistant = nextMessage.content,
                    ),
                )
                i += 2
            } else {
                i++
            }
        }

        return contextTurns
    }

    private fun buildProfileContext(profile: com.example.gymbro.shared.models.ai.AiUserProfile): String {
        return buildString {
            append("用户资料：")
            append("性别：${profile.gender}，")
            append("年龄：${profile.age}岁，")
            append("身高：${profile.height}cm，")
            append("体重：${profile.weight}kg，")
            append("健身经验：${profile.experience}")
            profile.bodyFatPercentage?.let { append("，体脂率：$it%") }
        }
    }

    private fun buildTemplatesContext(templates: List<com.example.gymbro.shared.models.ai.WorkoutTemplate>): String {
        return buildString {
            append("相关训练模板：\n")
            templates.take(3).forEach { template ->
                append("- ${template.name}：${template.description}")
                if (template.targetMuscles.isNotEmpty()) {
                    append("（目标肌群：${template.targetMuscles.joinToString("、")}）")
                }
                append("\n")
            }
        }
    }

    private suspend fun buildBasicPrompt(userPrompt: String): String {
        // 简化的prompt构建，避免promptModeManager依赖
        return buildString {
            append("[SYSTEM]\n")
            append("你是一个专业的健身教练AI助手。请提供专业、个性化的健身建议。\n\n")

            append("[USER]\n")
            append(userPrompt)
        }
    }

    fun handleDetectFunctionCall(effect: AiCoachContract.Effect.DetectFunctionCall) {
        handlerScope.launch {
            try {
                val message = com.example.gymbro.domain.coach.model.CoachMessage.AiMessage(
                    id = effect.messageId,
                    content = effect.content,
                    timestamp = System.currentTimeMillis(),
                )

                when (val result = detectActionableContentUseCase(message)) {
                    is ModernResult.Success -> {
                        val detection = result.data
                        detection.functionCallJson?.let { json ->
                            sendIntent(
                                AiCoachContract.Intent.FunctionCallDetected(effect.messageId, json),
                            )

                            when (detection.type) {
                                com.example.gymbro.domain.coach.usecase.ActionableType.TEMPLATE_GENERATION -> {
                                    sendIntent(
                                        AiCoachContract.Intent.TemplateGenerationRequested(
                                            effect.messageId,
                                            json,
                                        ),
                                    )
                                }
                                com.example.gymbro.domain.coach.usecase.ActionableType.PLAN_GENERATION -> {
                                    sendIntent(
                                        AiCoachContract.Intent.PlanGenerationRequested(
                                            effect.messageId,
                                            json,
                                        ),
                                    )
                                }
                                com.example.gymbro.domain.coach.usecase.ActionableType.EXERCISE_OPERATION -> {
                                    // 处理动作库操作：解析并执行动作相关的功能调用
                                    handleExerciseOperation(effect.messageId, json)
                                }
                                com.example.gymbro.domain.coach.usecase.ActionableType.SESSION_OPERATION -> {
                                    // 处理训练会话操作：解析并执行会话相关的功能调用
                                    handleSessionOperation(effect.messageId, json)
                                }
                                com.example.gymbro.domain.coach.usecase.ActionableType.NONE -> {
                                    // 无可操作内容
                                }
                            }
                        }
                    }
                    is ModernResult.Error -> {
                        Timber.e("Function Call检测失败: ${result.error}")
                        sendIntent(
                            AiCoachContract.Intent.FunctionCallProcessedResult(
                                effect.messageId,
                                AiCoachContract.FunctionCallResult(
                                    "detection",
                                    false,
                                    "检测失败: ${result.error}",
                                ),
                            ),
                        )
                    }
                    is ModernResult.Loading -> {
                        // 检测中，无需处理
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Function Call检测异常")
                sendIntent(
                    AiCoachContract.Intent.FunctionCallProcessedResult(
                        effect.messageId,
                        AiCoachContract.FunctionCallResult("detection", false, "检测异常: ${e.message}"),
                    ),
                )
            }
        }
    }

    private fun handleExerciseOperation(messageId: String, functionCallJson: String) {
        try {
            // 解析动作操作的具体类型和参数
            // 这里可以根据functionCallJson的内容判断是查询动作、添加动作等
            val actionTriggered = when {
                functionCallJson.contains("search", ignoreCase = true) -> "动作库搜索已执行"
                functionCallJson.contains("add", ignoreCase = true) -> "新动作已添加到动作库"
                functionCallJson.contains("modify", ignoreCase = true) -> "动作信息已更新"
                else -> "动作库操作已执行"
            }

            sendIntent(
                AiCoachContract.Intent.FunctionCallProcessedResult(
                    messageId,
                    AiCoachContract.FunctionCallResult(
                        "exercise_operation",
                        true,
                        functionCallJson,
                        actionTriggered = actionTriggered,
                    ),
                ),
            )
        } catch (e: Exception) {
            Timber.e(e, "动作库操作处理失败")
            sendIntent(
                AiCoachContract.Intent.FunctionCallProcessedResult(
                    messageId,
                    AiCoachContract.FunctionCallResult(
                        "exercise_operation",
                        false,
                        functionCallJson,
                        error = "动作库操作失败: ${e.message}",
                    ),
                ),
            )
        }
    }

    private fun handleSessionOperation(messageId: String, functionCallJson: String) {
        try {
            // 解析训练会话操作的具体类型和参数
            val actionTriggered = when {
                functionCallJson.contains("start", ignoreCase = true) -> "训练会话已开始"
                functionCallJson.contains("pause", ignoreCase = true) -> "训练会话已暂停"
                functionCallJson.contains("resume", ignoreCase = true) -> "训练会话已恢复"
                functionCallJson.contains("finish", ignoreCase = true) -> "训练会话已完成"
                functionCallJson.contains("record", ignoreCase = true) -> "训练数据已记录"
                else -> "训练会话操作已执行"
            }

            sendIntent(
                AiCoachContract.Intent.FunctionCallProcessedResult(
                    messageId,
                    AiCoachContract.FunctionCallResult(
                        "session_operation",
                        true,
                        functionCallJson,
                        actionTriggered = actionTriggered,
                    ),
                ),
            )
        } catch (e: Exception) {
            Timber.e(e, "训练会话操作处理失败")
            sendIntent(
                AiCoachContract.Intent.FunctionCallProcessedResult(
                    messageId,
                    AiCoachContract.FunctionCallResult(
                        "session_operation",
                        false,
                        functionCallJson,
                        error = "训练会话操作失败: ${e.message}",
                    ),
                ),
            )
        }
    }

    fun handleProcessTemplateGeneration(effect: AiCoachContract.Effect.ProcessTemplateGeneration) {
        handlerScope.launch {
            try {
                val generateFromResponseUseCase = templateGenerationUseCase.GenerateFromResponse()
                val params = TemplateGenerationUseCase.GenerateFromResponseParams(effect.functionCallJson)

                when (val result = generateFromResponseUseCase(params)) {
                    is ModernResult.Success -> {
                        val template = result.data
                        sendIntent(
                            AiCoachContract.Intent.FunctionCallProcessedResult(
                                effect.messageId,
                                AiCoachContract.FunctionCallResult(
                                    "template_generation",
                                    true,
                                    effect.functionCallJson,
                                    actionTriggered = "训练模板「${template.name}」生成成功",
                                ),
                            ),
                        )
                    }
                    is ModernResult.Error -> {
                        Timber.e("训练模板生成失败: ${result.error}")
                        sendIntent(
                            AiCoachContract.Intent.FunctionCallProcessedResult(
                                effect.messageId,
                                AiCoachContract.FunctionCallResult(
                                    "template_generation",
                                    false,
                                    effect.functionCallJson,
                                    error = "模板生成失败: ${result.error}",
                                ),
                            ),
                        )
                    }
                    is ModernResult.Loading -> {
                        // 生成中，无需处理
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "训练模板生成处理异常")
                sendIntent(
                    AiCoachContract.Intent.FunctionCallProcessedResult(
                        effect.messageId,
                        AiCoachContract.FunctionCallResult(
                            "template_generation",
                            false,
                            effect.functionCallJson,
                            error = "模板生成失败: ${e.message}",
                        ),
                    ),
                )
            }
        }
    }

    fun handleProcessPlanGeneration(effect: AiCoachContract.Effect.ProcessPlanGeneration) {
        handlerScope.launch {
            try {
                when (val result = generateStructuredPlanUseCase(effect.functionCallJson)) {
                    is ModernResult.Success -> {
                        when (val planResult = result.data) {
                            is com.example.gymbro.domain.workout.usecase.GeneratePlanResult.Success -> {
                                sendIntent(
                                    AiCoachContract.Intent.FunctionCallProcessedResult(
                                        effect.messageId,
                                        AiCoachContract.FunctionCallResult(
                                            "plan_generation",
                                            true,
                                            effect.functionCallJson,
                                            actionTriggered = "训练计划「${planResult.planName}」生成成功，时长${planResult.duration}",
                                        ),
                                    ),
                                )
                            }
                            is com.example.gymbro.domain.workout.usecase.GeneratePlanResult.Partial -> {
                                Timber.w("训练计划部分生成: planId=${planResult.planId}")
                                sendIntent(
                                    AiCoachContract.Intent.FunctionCallProcessedResult(
                                        effect.messageId,
                                        AiCoachContract.FunctionCallResult(
                                            "plan_generation",
                                            true,
                                            effect.functionCallJson,
                                            actionTriggered = "训练计划生成成功，但有${planResult.issues.size}个问题需要注意",
                                        ),
                                    ),
                                )
                            }
                        }
                    }
                    is ModernResult.Error -> {
                        Timber.e("训练计划生成失败: ${result.error}")
                        sendIntent(
                            AiCoachContract.Intent.FunctionCallProcessedResult(
                                effect.messageId,
                                AiCoachContract.FunctionCallResult(
                                    "plan_generation",
                                    false,
                                    effect.functionCallJson,
                                    error = "计划生成失败: ${result.error}",
                                ),
                            ),
                        )
                    }
                    is ModernResult.Loading -> {
                        // 生成中，无需处理
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "训练计划生成处理异常")
                sendIntent(
                    AiCoachContract.Intent.FunctionCallProcessedResult(
                        effect.messageId,
                        AiCoachContract.FunctionCallResult(
                            "plan_generation",
                            false,
                            effect.functionCallJson,
                            error = "计划生成失败: ${e.message}",
                        ),
                    ),
                )
            }
        }
    }
}
