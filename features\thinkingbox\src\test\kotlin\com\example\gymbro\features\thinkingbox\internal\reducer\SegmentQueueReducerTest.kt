package com.example.gymbro.features.thinkingbox.internal.reducer

import com.example.gymbro.features.thinkingbox.domain.model.Segment
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import org.junit.Before
import org.junit.Test
import java.util.ArrayDeque
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * SegmentQueueReducer测试 - Plan B重构版本
 *
 * 🔥 【Plan B重构】验证Segment队列管理逻辑，确保sessionId移除后的功能完整性
 */
class SegmentQueueReducerTest {

    private lateinit var reducer: SegmentQueueReducer

    @Before
    fun setup() {
        reducer = SegmentQueueReducer()
    }

    @Test
    fun `should initialize TBState correctly`() {
        // Given & When
        val state = SegmentQueueReducer.TBState(messageId = "test-123")

        // Then
        assertEquals("test-123", state.messageId)
        // 🔥 【Plan B重构】不再验证sessionId，通过ConversationIdManager获取
        assertNull(state.current)
        assertTrue(state.queue.isEmpty())
        assertTrue(state.finalBuffer.isEmpty())
        assertFalse(state.thinkingClosed)
        assertFalse(state.finalClosed)
        assertTrue(state.startTime > 0)
        assertEquals(0L, state.version)
    }

    @Test
    fun `should return next segment to render correctly`() {
        // Given - current段已关闭且有内容
        val currentSegment = Segment(
            id = "current-1",
            kind = SegmentKind.PHASE,
            title = "当前段",
            content = "当前内容",
            closed = true
        )
        val state = SegmentQueueReducer.TBState(
            messageId = "test-123",
            current = currentSegment
        )

        // When
        val nextSegment = state.getNextSegmentToRender()

        // Then
        assertNotNull(nextSegment)
        assertEquals("current-1", nextSegment.id)
        assertEquals("当前内容", nextSegment.content)
    }

    @Test
    fun `should determine shouldCloseThinkingBox correctly`() {
        // Given - 思考已关闭且队列为空
        val state = SegmentQueueReducer.TBState(
            messageId = "test-123",
            thinkingClosed = true,
            queue = ArrayDeque()
        )

        // When & Then
        assertTrue(state.shouldCloseThinkingBox())

        // Given - 思考未关闭
        val stateThinkingOpen = state.copy(thinkingClosed = false)

        // When & Then
        assertFalse(stateThinkingOpen.shouldCloseThinkingBox())
    }

    @Test
    fun `should determine isFinalReadyToRender correctly`() {
        // Given - 思考已关闭，有final内容，UI空闲
        val state = SegmentQueueReducer.TBState(
            messageId = "test-123",
            thinkingClosed = true,
            finalBuffer = StringBuilder("最终内容"),
            queue = ArrayDeque()
        )

        // When & Then
        assertTrue(state.isFinalReadyToRender())

        // Given - 思考未关闭
        val stateThinkingOpen = state.copy(thinkingClosed = false)

        // When & Then
        assertFalse(stateThinkingOpen.isFinalReadyToRender())
    }

    @Test
    fun `should determine isStreaming correctly`() {
        // Given - 思考未关闭
        val stateThinkingOpen = SegmentQueueReducer.TBState(
            messageId = "test-123",
            thinkingClosed = false,
            finalClosed = true
        )

        // When & Then
        assertTrue(stateThinkingOpen.isStreaming())

        // Given - 都已关闭
        val stateClosed = SegmentQueueReducer.TBState(
            messageId = "test-123",
            thinkingClosed = true,
            finalClosed = true
        )

        // When & Then
        assertFalse(stateClosed.isStreaming())
    }

    @Test
    fun `should handle SegmentStart event correctly`() {
        // Given
        val initialState = SegmentQueueReducer.TBState(messageId = "test-123")
        val event = ThinkingEvent.SegmentStarted(
            id = "segment-1",
            kind = SegmentKind.PHASE,
            title = "思考阶段"
        )

        // When
        val result = reducer.reduce(initialState, event)

        // Then
        assertNotNull(result.state.current)
        assertEquals("segment-1", result.state.current?.id)
        assertEquals(SegmentKind.PHASE, result.state.current?.kind)
        assertEquals("思考阶段", result.state.current?.title)
        assertEquals("", result.state.current?.content)
        assertFalse(result.state.current?.closed ?: true)
        assertEquals(initialState.version + 1, result.state.version)
    }

    @Test
    fun `should handle SegmentContent event correctly`() {
        // Given - 有当前段
        val currentSegment = Segment(
            id = "segment-1",
            kind = SegmentKind.PHASE,
            title = "思考阶段",
            content = "现有内容",
            closed = false
        )
        val initialState = SegmentQueueReducer.TBState(
            messageId = "test-123",
            current = currentSegment
        )
        val event = ThinkingEvent.SegmentText("新增内容")

        // When
        val result = reducer.reduce(initialState, event)

        // Then
        assertNotNull(result.state.current)
        assertEquals("现有内容新增内容", result.state.current?.content)
        assertEquals(initialState.version + 1, result.state.version)
    }

    @Test
    fun `should handle SegmentClosed event correctly`() {
        // Given - 有当前段
        val currentSegment = Segment(
            id = "segment-1",
            kind = SegmentKind.PHASE,
            title = "思考阶段",
            content = "段内容",
            closed = false
        )
        val initialState = SegmentQueueReducer.TBState(
            messageId = "test-123",
            current = currentSegment
        )
        val event = ThinkingEvent.SegmentClosed(id = "segment-1")

        // When
        val result = reducer.reduce(initialState, event)

        // Then
        assertNull(result.state.current)
        assertEquals(1, result.state.queue.size)
        val queuedSegment = result.state.queue.first()
        assertEquals("segment-1", queuedSegment.id)
        assertTrue(queuedSegment.closed)
        assertEquals("段内容", queuedSegment.content)
        assertEquals(initialState.version + 1, result.state.version)
    }

    @Test
    fun `should handle ThinkingClosed event correctly`() {
        // Given
        val initialState = SegmentQueueReducer.TBState(
            messageId = "test-123",
            thinkingClosed = false
        )
        val event = ThinkingEvent.ThinkingClosed

        // When
        val result = reducer.reduce(initialState, event)

        // Then
        assertTrue(result.state.thinkingClosed)
        assertEquals(initialState.version + 1, result.state.version)
    }

    @Test
    fun `should handle FinalContent event correctly`() {
        // Given
        val initialState = SegmentQueueReducer.TBState(
            messageId = "test-123",
            finalBuffer = StringBuilder("现有内容")
        )
        val event = ThinkingEvent.FinalContent(text = "新增内容")

        // When
        val result = reducer.reduce(initialState, event)

        // Then
        assertEquals("现有内容新增内容", result.state.finalBuffer.toString())
        assertEquals(initialState.version + 1, result.state.version)
    }
}
